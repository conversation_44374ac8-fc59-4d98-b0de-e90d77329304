# nnU-Net Pipeline

Pipeline complet pour l'entraînement et l'inférence avec nnU-Net, incluant des utilitaires pour la visualisation et l'analyse des résultats.

## 📋 Table des matières

- [Installation](#installation)
- [Configuration des chemins](#configuration-des-chemins)
- [Scripts principaux](#scripts-principaux)
- [Scripts utilitaires](#scripts-utilitaires)
- [Structure des dossiers](#structure-des-dossiers)
- [Utilisation](#utilisation)

## 🚀 Installation

### 1. Installation de nnU-Net

```bash
# Créer un environnement conda
conda create -n nnunet_env python=3.10 -y
conda activate nnunet_env

# Installer PyTorch avec support CUDA
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia

# Vérifier l'installation CUDA
python -c "import torch; print(torch.__version__); print(torch.cuda.is_available())"

# Installer nnU-Net
git clone https://github.com/MIC-DKFZ/nnUNet.git
cd nnUNet
pip install -e .
```

### 2. Installation des dépendances du pipeline

```bash
# Installer toutes les dépendances depuis le fichier requirements.txt
pip install -r requirements.txt
```

## 📁 Configuration des chemins

### Linux/Unix
Exécutez le script de configuration :
```bash
chmod +x new_path.sh
sudo ./new_path.sh
# Puis changez les permissions pour votre utilisateur :
sudo chown -R $USER:$USER /mnt/datasets/nnUnet/nnUnet_raw
sudo chown -R $USER:$USER /mnt/datasets/nnUnet/nnUnet_preprocessed
sudo chown -R $USER:$USER /mnt/results/nnUnet_results
```

### Windows
Avant d'exécuter la commande ci-dessous, ouvrez et modifiez le fichier `new_path_windows.ps1` afin d'y renseigner les chemins adaptés à votre machine (par exemple les dossiers `nnUNet_raw`, `nnUNet_preprocessed`, `nnUNet_results`).

Exécutez le script PowerShell :
```powershell
.\new_path_windows.ps1
```

## 📜 Scripts principaux

### `train_nnunet.py`
**Fonction** : Entraînement complet d'un modèle nnU-Net avec validation croisée

**Caractéristiques** :
- Support multi-plateforme (Windows/Linux)
- Validation croisée 5-fold configurable
- Gestion automatique des trainers selon le nombre d'époques
- Logging détaillé avec horodatage
- Prétraitement automatique des données

**Configuration** :
```python
DATASET_ID = "011"           # ID du dataset
CONFIGURATION = "2d"         # Configuration nnU-Net
EPOCHS = 5                   # Nombre d'époques
USE_CROSS_VALIDATION = True  # Validation croisée
VALIDATION_FOLDS = [0, 1, 2, 3, 4, "all"]  # Folds à entraîner
```

**Utilisation** :
```bash
python train_nnunet.py
```

### `infer_nnunet.py`
**Fonction** : Inférence avec post-traitement automatique et visualisation

**Caractéristiques** :
- Inférence avec modèles de validation croisée ou modèle unique
- Post-traitement automatique des masques
- Création d'overlays avec contours
- Analyse des labels détectés
- Génération de vidéos à partir des overlays
- Validation automatique des noms de fichiers

**Utilisation** :
```bash
# Avec dossier par défaut
python infer_nnunet.py

# Avec dossier personnalisé
python infer_nnunet.py --input_folder "chemin/vers/images"
```

**Format requis** : Les images doivent être nommées `XXXX_0000.png` (ex: `0001_0000.png`)

### `run_batch_inference.py`
**Fonction** : Traitement par lot de plusieurs dossiers d'images

**Caractéristiques** :
- Traitement automatique de tous les sous-dossiers
- Validation des noms de fichiers (optionnelle)
- Gestion des erreurs avec continuation
- Logging détaillé pour chaque dossier
- Timeout de sécurité (2h par dossier)

**Configuration** :
```python
PARENT_FOLDER = r"C:\chemin\vers\dossier\parent"  # Dossier contenant les sous-dossiers
SKIP_VALIDATION = False      # Ignorer la validation des noms
CONTINUE_ON_ERROR = True     # Continuer malgré les erreurs
```

**Utilisation** :
```bash
python run_batch_inference.py
```

### `export_model_zip.py`
**Fonction** : Export des modèles entraînés au format ZIP

**Caractéristiques** :
- Export de folds individuels ou combinés
- Vérification automatique de la disponibilité des modèles
- Support des modes validation croisée et "all"
- Génération automatique du nom de fichier

**Configuration** :
```python
DATASET_ID = "011"
VALIDATION_FOLDS = ["all"]  # ou [0, 1, 2, 3, 4] ou [0, 1, "all"]
```

**Utilisation** :
```bash
python export_model_zip.py
```

## 🛠️ Scripts utilitaires

### `utils/rename_for_training.py`
Renomme les fichiers pour l'entraînement nnU-Net (format `XXXX_0000.png`)

### `utils/rename_for_inference.py`
Renomme les fichiers pour l'inférence nnU-Net

### `utils/visualisation.py`
Fonctions de reconversion et visualisation des masques prédits

### `utils/overlay_manager.py`
Gestion des overlays avec différents styles (contours, transparence)

### `utils/label_analyzer.py`
Analyse statistique des labels dans les masques de segmentation

### `utils/png_to_video.py`
Création de vidéos à partir de séquences d'images PNG

## 📂 Structure des dossiers

```
nnunet_pipeline/
├── train_nnunet.py           # Script d'entraînement
├── infer_nnunet.py           # Script d'inférence
├── run_batch_inference.py    # Traitement par lot
├── export_model_zip.py       # Export de modèles
├── new_path.sh              # Configuration Linux
├── new_path_windows.ps1     # Configuration Windows
├── utils/                   # Utilitaires
│   ├── rename_for_training.py
│   ├── rename_for_inference.py
│   ├── visualisation.py
│   ├── overlay_manager.py
│   ├── label_analyzer.py
│   └── png_to_video.py
└── logs/                    # Logs d'entraînement
```

## 🎯 Utilisation

### Workflow complet

1. **Configuration initiale** :
   ```bash
   # Linux
   sudo ./new_path.sh
   
   # Windows
   .\new_path_windows.ps1
   ```

2. **Entraînement** :
   ```bash
   python train_nnunet.py
   ```

3. **Inférence simple** :
   ```bash
   python infer_nnunet.py --input_folder "chemin/vers/images"
   ```

4. **Inférence par lot** :
   ```bash
   # Configurer PARENT_FOLDER dans le script
   python run_batch_inference.py
   ```

5. **Export du modèle** :
   ```bash
   python export_model_zip.py
   ```

### Résultats générés

- **Entraînement** : Modèles dans `nnUnet_results/`
- **Inférence** : 
  - Masques bruts dans `dossier_sortie/`
  - Masques reconvertis dans `dossier_sortie/reconverted_masks/`
  - Overlays dans `dossier_sortie/overlays/`
  - Vidéo dans `dossier_sortie/overlays/`
  - Rapport d'analyse dans `dossier_sortie/`

## ⚙️ Configuration avancée

Tous les scripts utilisent une détection automatique du système d'exploitation et adaptent les chemins en conséquence. Les paramètres principaux sont configurables en début de chaque script dans la section `=== CONFIGURATION ===`.
